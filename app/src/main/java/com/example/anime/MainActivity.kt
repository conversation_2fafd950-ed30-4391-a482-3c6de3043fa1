package com.example.anime

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.os.Message
import android.view.View
import android.webkit.*
import android.widget.ProgressBar
import androidx.appcompat.app.AppCompatActivity

class MainActivity : AppCompatActivity() {
    
    private lateinit var webView: WebView
    private lateinit var progressBar: ProgressBar
    private lateinit var sharedPreferences: SharedPreferences
    
    companion object {
        private const val PREFS_NAME = "HiAnimeViewerPrefs"
        private const val LAST_URL_KEY = "last_url"
        private const val DEFAULT_URL = "https://hianime.to"
        private const val HIANIME_DOMAIN = "hianime.to"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        // Initialize SharedPreferences for URL persistence
        sharedPreferences = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        
        // Initialize views
        webView = findViewById(R.id.webView)
        progressBar = findViewById(R.id.progressBar)
        
        setupWebView()
        loadLastUrl()
    }
    
    private fun setupWebView() {
        // Configure WebView settings
        webView.settings.apply {
            javaScriptEnabled = true
            setSupportMultipleWindows(false)
            javaScriptCanOpenWindowsAutomatically = false
            domStorageEnabled = true
            loadWithOverviewMode = true
            useWideViewPort = true
            builtInZoomControls = true
            displayZoomControls = false
            setSupportZoom(true)
            allowFileAccess = false
            allowContentAccess = false
            allowFileAccessFromFileURLs = false
            allowUniversalAccessFromFileURLs = false
        }
        
        // Set up WebChromeClient to block popups and handle progress
        webView.webChromeClient = object : WebChromeClient() {
            override fun onCreateWindow(
                view: WebView, isDialog: Boolean, isUserGesture: Boolean, resultMsg: Message
            ): Boolean = false // Block all popup windows
            
            override fun onJsAlert(
                view: WebView, url: String, message: String, result: JsResult
            ): Boolean {
                result.cancel()
                return true // Block JS alerts
            }
            
            override fun onJsConfirm(
                view: WebView, url: String, message: String, result: JsResult
            ): Boolean {
                result.cancel()
                return true // Block JS confirms
            }
            
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                if (newProgress < 100) {
                    progressBar.visibility = View.VISIBLE
                    progressBar.progress = newProgress
                } else {
                    progressBar.visibility = View.GONE
                }
            }
        }
        
        // Set up WebViewClient to handle URL loading and redirects
        webView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(
                view: WebView, request: WebResourceRequest
            ): Boolean {
                val url = request.url.toString()
                
                // Block fake popup links like https://hianime.to/#
                if (url.startsWith("https://hianime.to/#")) {
                    return true // Block the navigation
                }
                
                // Only allow navigation within hianime.to domain
                return if (url.contains(HIANIME_DOMAIN)) {
                    // Save the URL for persistence
                    saveCurrentUrl(url)
                    false // Allow navigation
                } else {
                    true // Block external redirects
                }
            }
            
            override fun onPageFinished(view: WebView, url: String) {
                super.onPageFinished(view, url)
                
                // Save the current URL
                saveCurrentUrl(url)
                
                // Inject CSS to hide popup overlays and ads
                view.evaluateJavascript("""
                    (function(){
                        const css = `.popup, .overlay, .modal, .advertisement, .ad-banner, 
                                     .popup-overlay, .modal-overlay, .ad-container,
                                     [class*="popup"], [class*="overlay"], [class*="modal"],
                                     [id*="popup"], [id*="overlay"], [id*="modal"],
                                     [class*="ad-"], [id*="ad-"] { 
                                         display: none !important; 
                                         visibility: hidden !important;
                                         opacity: 0 !important;
                                         pointer-events: none !important;
                                     }`;
                        let style = document.createElement('style');
                        style.appendChild(document.createTextNode(css));
                        document.head.appendChild(style);
                        
                        // Also remove any elements that might be ads or popups
                        setTimeout(function() {
                            const suspiciousElements = document.querySelectorAll(
                                '[class*="popup"], [class*="overlay"], [class*="modal"], [class*="ad-"]'
                            );
                            suspiciousElements.forEach(el => el.remove());
                        }, 1000);
                    })();
                """.trimIndent(), null)
            }
            
            override fun onReceivedError(
                view: WebView?, request: WebResourceRequest?, error: WebResourceError?
            ) {
                super.onReceivedError(view, request, error)
                // Handle errors gracefully
            }
        }
    }
    
    private fun loadLastUrl() {
        val lastUrl = sharedPreferences.getString(LAST_URL_KEY, DEFAULT_URL) ?: DEFAULT_URL
        webView.loadUrl(lastUrl)
    }
    
    private fun saveCurrentUrl(url: String) {
        // Only save URLs from hianime.to domain
        if (url.contains(HIANIME_DOMAIN)) {
            sharedPreferences.edit()
                .putString(LAST_URL_KEY, url)
                .apply()
        }
    }
    
    override fun onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack()
        } else {
            super.onBackPressed()
        }
    }
    
    override fun onPause() {
        super.onPause()
        // Save current URL when app goes to background
        saveCurrentUrl(webView.url ?: DEFAULT_URL)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        webView.destroy()
    }
}
