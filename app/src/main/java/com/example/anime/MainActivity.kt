package com.example.anime

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.os.Message
import android.view.View
import android.view.WindowManager
import android.webkit.*
import android.widget.ProgressBar
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat

class MainActivity : AppCompatActivity() {
    
    private lateinit var webView: WebView
    private lateinit var progressBar: ProgressBar
    private lateinit var sharedPreferences: SharedPreferences
    
    companion object {
        private const val PREFS_NAME = "HiAnimeViewerPrefs"
        private const val LAST_URL_KEY = "last_url"
        private const val DEFAULT_URL = "https://hianime.to"
        private const val HIANIME_DOMAIN = "hianime.to"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Hide the action bar/app bar
        supportActionBar?.hide()

        // Make the app fullscreen and hide system UI
        WindowCompat.setDecorFitsSystemWindows(window, false)
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
        windowInsetsController.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        windowInsetsController.hide(WindowInsetsCompat.Type.systemBars())

        // Keep screen on while using the app
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        setContentView(R.layout.activity_main)

        // Initialize SharedPreferences for URL persistence
        sharedPreferences = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

        // Initialize views
        webView = findViewById(R.id.webView)
        progressBar = findViewById(R.id.progressBar)

        setupWebView()
        loadLastUrl()
    }
    
    private fun setupWebView() {
        // Configure WebView settings
        webView.settings.apply {
            javaScriptEnabled = true
            setSupportMultipleWindows(false)
            javaScriptCanOpenWindowsAutomatically = false
            domStorageEnabled = true
            loadWithOverviewMode = true
            useWideViewPort = true
            builtInZoomControls = true
            displayZoomControls = false
            setSupportZoom(true)
            allowFileAccess = false
            allowContentAccess = false
            allowFileAccessFromFileURLs = false
            allowUniversalAccessFromFileURLs = false
        }
        
        // Set up WebChromeClient to block popups and handle progress
        webView.webChromeClient = object : WebChromeClient() {
            override fun onCreateWindow(
                view: WebView, isDialog: Boolean, isUserGesture: Boolean, resultMsg: Message
            ): Boolean = false // Block all popup windows

            override fun onJsAlert(
                view: WebView, url: String, message: String, result: JsResult
            ): Boolean {
                result.cancel()
                return true // Block JS alerts
            }

            override fun onJsConfirm(
                view: WebView, url: String, message: String, result: JsResult
            ): Boolean {
                result.cancel()
                return true // Block JS confirms
            }

            override fun onJsPrompt(
                view: WebView, url: String, message: String, defaultValue: String, result: JsPromptResult
            ): Boolean {
                result.cancel()
                return true // Block JS prompts
            }

            override fun onJsBeforeUnload(
                view: WebView, url: String, message: String, result: JsResult
            ): Boolean {
                result.cancel()
                return true // Block beforeunload popups
            }

            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                if (newProgress < 100) {
                    progressBar.visibility = View.VISIBLE
                    progressBar.progress = newProgress
                } else {
                    progressBar.visibility = View.GONE
                }
            }
        }
        
        // Set up WebViewClient to handle URL loading and redirects
        webView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(
                view: WebView, request: WebResourceRequest
            ): Boolean {
                val url = request.url.toString()

                // Block fake popup links like https://hianime.to/#
                if (url.startsWith("https://hianime.to/#")) {
                    return true // Block the navigation
                }

                // Block common popup/ad URLs
                val blockedPatterns = listOf(
                    "javascript:", "about:blank", "data:", "blob:",
                    "popup", "ad.", "ads.", "doubleclick", "googleads",
                    "googlesyndication", "amazon-adsystem", "facebook.com/tr"
                )

                if (blockedPatterns.any { url.contains(it, ignoreCase = true) }) {
                    return true // Block suspicious URLs
                }

                // Only allow navigation within hianime.to domain
                return if (url.contains(HIANIME_DOMAIN)) {
                    // Save the URL for persistence
                    saveCurrentUrl(url)
                    false // Allow navigation
                } else {
                    true // Block external redirects
                }
            }
            
            override fun onPageFinished(view: WebView, url: String) {
                super.onPageFinished(view, url)

                // Save the current URL
                saveCurrentUrl(url)

                // Enhanced popup and ad blocking with navigation bar removal
                view.evaluateJavascript("""
                    (function(){
                        // Hide navigation bar and other UI elements - SPECIFICALLY targeting HiAnime elements
                        const hideElements = `
                            /* Hide HiAnime specific navigation elements */
                            #header, .header, .site-header, .main-header, .top-header,
                            .navbar, .nav-bar, .navigation, .top-bar, .page-header, .app-header,
                            .fixed-top, .sticky-top, .header-wrapper, .header-container,
                            [class*="navbar"], [class*="nav-bar"], [class*="header"], [class*="top-bar"],
                            [id*="header"], [id*="nav"], [class*="nav"], .nav,

                            /* Hide popups and overlays */
                            .popup, .overlay, .modal, .advertisement, .ad-banner, .popup-overlay,
                            .modal-overlay, .ad-container, .toast, .notification, .alert,
                            [class*="popup"], [class*="overlay"], [class*="modal"], [class*="toast"],
                            [id*="popup"], [id*="overlay"], [id*="modal"], [id*="toast"],

                            /* Hide ads */
                            .ad, .ads, .advert, .advertisement, .banner, .sponsor,
                            [class*="ad-"], [class*="ads-"], [id*="ad-"], [id*="ads-"],
                            [class*="advert"], [class*="banner"], [class*="sponsor"],

                            /* Hide common popup containers */
                            .fancybox, .lightbox, .colorbox, .thickbox, .shadowbox,
                            .dialog, .tooltip, .dropdown-menu, .context-menu,

                            /* Hide video overlays and controls that might be ads */
                            .video-overlay, .player-overlay, .controls-overlay,
                            [class*="overlay"], [class*="cover"],

                            /* Hide notification bars */
                            .notification-bar, .cookie-notice, .gdpr-notice, .privacy-notice
                            {
                                display: none !important;
                                visibility: hidden !important;
                                opacity: 0 !important;
                                pointer-events: none !important;
                                position: absolute !important;
                                left: -9999px !important;
                                top: -9999px !important;
                                width: 0 !important;
                                height: 0 !important;
                                z-index: -1 !important;
                            }

                            /* Ensure main content takes full space */
                            body, html {
                                margin: 0 !important;
                                padding: 0 !important;
                                overflow-x: hidden !important;
                            }

                            /* AGGRESSIVE HEADER HIDING - Target any element at the top */
                            *[style*="position: fixed"], *[style*="position:fixed"],
                            *[style*="top: 0"], *[style*="top:0"] {
                                display: none !important;
                            }

                            /* Hide elements that are likely headers based on position */
                            body > *:first-child,
                            body > div:first-child,
                            html > body > *:first-child {
                                display: none !important;
                            }

                            /* Force hide any element containing HiAnime text that's positioned like a header */
                            *:contains("HiAnime"), *:contains("Hi Anime") {
                                display: none !important;
                            }
                        `;

                        let style = document.createElement('style');
                        style.appendChild(document.createTextNode(hideElements));
                        document.head.appendChild(style);

                        // Function to remove elements
                        function removeUnwantedElements() {
                            // Remove HiAnime specific navigation elements - AGGRESSIVE TARGETING
                            const navSelectors = [
                                '#header', '.header', '.site-header', '.main-header', '.page-header',
                                '.app-header', '.top-header', '.header-wrapper', '.header-container',
                                '.navbar', '.nav-bar', '.navigation', '.top-bar', '.nav',
                                '[class*="navbar"]', '[class*="nav-bar"]', '[class*="header"]',
                                '[class*="top-bar"]', '[id*="header"]', '[id*="nav"]', '[class*="nav"]',
                                '.fixed-top', '.sticky-top', 'header', 'nav'
                            ];

                            // Remove popup elements
                            const popupSelectors = [
                                '.popup', '.overlay', '.modal', '.toast', '.notification',
                                '.alert', '.dialog', '.tooltip', '.fancybox', '.lightbox',
                                '[class*="popup"]', '[class*="overlay"]', '[class*="modal"]',
                                '[id*="popup"]', '[id*="overlay"]', '[id*="modal"]'
                            ];

                            // Remove ad elements
                            const adSelectors = [
                                '.ad', '.ads', '.advert', '.advertisement', '.banner',
                                '.sponsor', '[class*="ad-"]', '[class*="ads-"]',
                                '[id*="ad-"]', '[id*="ads-"]', '[class*="advert"]',
                                '[class*="banner"]', '[class*="sponsor"]'
                            ];

                            [...navSelectors, ...popupSelectors, ...adSelectors].forEach(selector => {
                                try {
                                    document.querySelectorAll(selector).forEach(el => {
                                        if (el && el.parentNode) {
                                            el.parentNode.removeChild(el);
                                        }
                                    });
                                } catch (e) {
                                    // Ignore errors for invalid selectors
                                }
                            });

                            // Remove any fixed positioned elements
                            document.querySelectorAll('*').forEach(el => {
                                const style = window.getComputedStyle(el);
                                if (style.position === 'fixed' &&
                                    (el.offsetHeight > 50 || el.offsetWidth > 200)) {
                                    // Likely a popup or navigation bar
                                    if (el.parentNode) {
                                        el.parentNode.removeChild(el);
                                    }
                                }
                            });

                            // SPECIFIC HIANIME HEADER REMOVAL - Check for elements containing "HiAnime" text
                            document.querySelectorAll('*').forEach(el => {
                                const text = el.textContent || el.innerText || '';
                                const rect = el.getBoundingClientRect();

                                // Remove elements that:
                                // 1. Contain "HiAnime" text
                                // 2. Are positioned at the top of the page (y < 100)
                                // 3. Have significant width (likely header)
                                if ((text.toLowerCase().includes('hianime') ||
                                     text.toLowerCase().includes('hi anime')) &&
                                    rect.top < 100 && rect.width > 200) {
                                    console.log('Removing HiAnime header element:', el);
                                    if (el.parentNode) {
                                        el.parentNode.removeChild(el);
                                    }
                                }

                                // Also remove any element that's at the very top and spans most of the width
                                if (rect.top <= 10 && rect.width > window.innerWidth * 0.8 &&
                                    rect.height > 30 && rect.height < 150) {
                                    console.log('Removing top header element:', el);
                                    if (el.parentNode) {
                                        el.parentNode.removeChild(el);
                                    }
                                }
                            });
                        }

                        // Run immediately
                        removeUnwantedElements();

                        // Run again after delays to catch dynamically loaded content
                        setTimeout(removeUnwantedElements, 1000);
                        setTimeout(removeUnwantedElements, 3000);
                        setTimeout(removeUnwantedElements, 5000);

                        // Set up mutation observer to catch new popups
                        const observer = new MutationObserver(function(mutations) {
                            mutations.forEach(function(mutation) {
                                if (mutation.addedNodes.length > 0) {
                                    setTimeout(removeUnwantedElements, 100);
                                }
                            });
                        });

                        observer.observe(document.body, {
                            childList: true,
                            subtree: true
                        });

                        // Block common popup events
                        window.addEventListener('beforeunload', function(e) {
                            e.preventDefault();
                            return undefined;
                        });

                        // Override common popup functions
                        window.open = function() { return null; };
                        window.alert = function() { return false; };
                        window.confirm = function() { return false; };
                        window.prompt = function() { return null; };

                    })();
                """.trimIndent(), null)
            }
            
            override fun shouldInterceptRequest(
                view: WebView?, request: WebResourceRequest?
            ): WebResourceResponse? {
                val url = request?.url?.toString() ?: ""

                // Block ad and tracking domains
                val blockedDomains = listOf(
                    "doubleclick.net", "googleadservices.com", "googlesyndication.com",
                    "amazon-adsystem.com", "facebook.com/tr", "google-analytics.com",
                    "googletagmanager.com", "adsystem.amazon.com", "ads.yahoo.com",
                    "bing.com/ads", "outbrain.com", "taboola.com", "criteo.com",
                    "adsystem", "adnxs.com", "adsystem.amazon", "ads.", "/ads/",
                    "popup", "popunder", "interstitial"
                )

                if (blockedDomains.any { url.contains(it, ignoreCase = true) }) {
                    // Return empty response to block the request
                    return WebResourceResponse("text/plain", "utf-8", null)
                }

                return super.shouldInterceptRequest(view, request)
            }

            override fun onReceivedError(
                view: WebView?, request: WebResourceRequest?, error: WebResourceError?
            ) {
                super.onReceivedError(view, request, error)
                // Handle errors gracefully
            }
        }
    }
    
    private fun loadLastUrl() {
        val lastUrl = sharedPreferences.getString(LAST_URL_KEY, DEFAULT_URL) ?: DEFAULT_URL
        webView.loadUrl(lastUrl)
    }
    
    private fun saveCurrentUrl(url: String) {
        // Only save URLs from hianime.to domain
        if (url.contains(HIANIME_DOMAIN)) {
            sharedPreferences.edit()
                .putString(LAST_URL_KEY, url)
                .apply()
        }
    }
    
    override fun onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack()
        } else {
            super.onBackPressed()
        }
    }
    
    override fun onPause() {
        super.onPause()
        // Save current URL when app goes to background
        saveCurrentUrl(webView.url ?: DEFAULT_URL)
    }
    
    override fun onResume() {
        super.onResume()
        // Ensure fullscreen mode is maintained when app resumes
        hideSystemUI()
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
            hideSystemUI()
        }
    }

    private fun hideSystemUI() {
        // Hide system bars for immersive fullscreen experience
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
        windowInsetsController.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        windowInsetsController.hide(WindowInsetsCompat.Type.systemBars())
    }

    override fun onDestroy() {
        super.onDestroy()
        webView.destroy()
    }
}
