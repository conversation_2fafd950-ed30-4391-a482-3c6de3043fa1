package com.example.anime

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.os.Message
import android.view.View
import android.view.WindowManager
import android.webkit.*
import android.widget.ProgressBar
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat

class MainActivity : AppCompatActivity() {

    private lateinit var webView: WebView
    private lateinit var progressBar: ProgressBar
    private lateinit var sharedPreferences: SharedPreferences
    private var loadingTimeoutRunnable: Runnable? = null
    private val handler = android.os.Handler(android.os.Looper.getMainLooper())

    companion object {
        private const val PREFS_NAME = "HiAnimeViewerPrefs"
        private const val LAST_URL_KEY = "last_url"
        private const val DEFAULT_URL = "https://hianime.to"
        private const val HIANIME_DOMAIN = "hianime.to"
        private const val LOADING_TIMEOUT_MS = 15000L // 15 seconds timeout
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Hide the action bar/app bar
        supportActionBar?.hide()

        // Make the app truly fullscreen with notch support
        window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        window.addFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED)

        // Set black background to prevent white flash
        window.decorView.setBackgroundColor(android.graphics.Color.BLACK)

        // Enable fullscreen with notch support (API 28+)
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
            window.attributes.layoutInDisplayCutoutMode =
                WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
        }

        WindowCompat.setDecorFitsSystemWindows(window, false)

        setContentView(R.layout.activity_main)

        // Initialize SharedPreferences for URL persistence
        sharedPreferences = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

        // Initialize views
        webView = findViewById(R.id.webView)
        progressBar = findViewById(R.id.progressBar)

        // Set WebView background to black to prevent white flash
        webView.setBackgroundColor(android.graphics.Color.BLACK)

        setupWebView()
        hideSystemUI()
        loadLastUrl()
    }
    
    private fun setupWebView() {
        // Configure WebView settings
        webView.settings.apply {
            javaScriptEnabled = true
            setSupportMultipleWindows(false)
            javaScriptCanOpenWindowsAutomatically = false
            domStorageEnabled = true
            loadWithOverviewMode = true
            useWideViewPort = true
            builtInZoomControls = true
            displayZoomControls = false
            setSupportZoom(true)
            allowFileAccess = false
            allowContentAccess = false
            allowFileAccessFromFileURLs = false
            allowUniversalAccessFromFileURLs = false

            // Prevent white screen issues
            cacheMode = WebSettings.LOAD_DEFAULT
            mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            mediaPlaybackRequiresUserGesture = false

            // Improve rendering performance
            setRenderPriority(WebSettings.RenderPriority.HIGH)
            setEnableSmoothTransition(true)
        }
        
        // Set up WebChromeClient to block popups and handle progress
        webView.webChromeClient = object : WebChromeClient() {
            override fun onCreateWindow(
                view: WebView, isDialog: Boolean, isUserGesture: Boolean, resultMsg: Message
            ): Boolean = false // Block all popup windows

            override fun onJsAlert(
                view: WebView, url: String, message: String, result: JsResult
            ): Boolean {
                result.cancel()
                return true // Block JS alerts
            }

            override fun onJsConfirm(
                view: WebView, url: String, message: String, result: JsResult
            ): Boolean {
                result.cancel()
                return true // Block JS confirms
            }

            override fun onJsPrompt(
                view: WebView, url: String, message: String, defaultValue: String, result: JsPromptResult
            ): Boolean {
                result.cancel()
                return true // Block JS prompts
            }

            override fun onJsBeforeUnload(
                view: WebView, url: String, message: String, result: JsResult
            ): Boolean {
                result.cancel()
                return true // Block beforeunload popups
            }

            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                if (newProgress < 100) {
                    progressBar.visibility = View.VISIBLE
                    progressBar.progress = newProgress
                } else {
                    progressBar.visibility = View.GONE
                }
            }
        }
        
        // Set up WebViewClient to handle URL loading and redirects
        webView.webViewClient = object : WebViewClient() {
            override fun onPageStarted(view: WebView?, url: String?, favicon: android.graphics.Bitmap?) {
                super.onPageStarted(view, url, favicon)
                // Keep black background while loading to prevent white flash
                view?.setBackgroundColor(android.graphics.Color.BLACK)

                // Set up timeout to detect stuck loading
                loadingTimeoutRunnable?.let { handler.removeCallbacks(it) }
                loadingTimeoutRunnable = Runnable {
                    android.util.Log.d("ANIME", "Page loading timeout for: $url")
                    runOnUiThread {
                        if (view?.canGoBack() == true) {
                            view.goBack()
                        } else {
                            view?.loadUrl(DEFAULT_URL)
                        }
                    }
                }
                handler.postDelayed(loadingTimeoutRunnable!!, LOADING_TIMEOUT_MS)
            }

            override fun shouldOverrideUrlLoading(
                view: WebView, request: WebResourceRequest
            ): Boolean {
                val url = request.url.toString()

                // Block fake popup links like https://hianime.to/#
                if (url.startsWith("https://hianime.to/#")) {
                    return true // Block the navigation
                }

                // Block common popup/ad URLs
                val blockedPatterns = listOf(
                    "javascript:", "about:blank", "data:", "blob:",
                    "popup", "ad.", "ads.", "doubleclick", "googleads",
                    "googlesyndication", "amazon-adsystem", "facebook.com/tr"
                )

                if (blockedPatterns.any { url.contains(it, ignoreCase = true) }) {
                    return true // Block suspicious URLs
                }

                // Only allow navigation within hianime.to domain
                return if (url.contains(HIANIME_DOMAIN)) {
                    // Save the URL for persistence
                    saveCurrentUrl(url)
                    false // Allow navigation
                } else {
                    // Block external redirects but handle gracefully
                    // Instead of causing black screen, stay on current page
                    android.util.Log.d("ANIME", "Blocked external redirect to: $url")

                    // If we have history, go back, otherwise reload current page
                    runOnUiThread {
                        if (view.canGoBack()) {
                            view.goBack()
                        } else {
                            // Reload the last known good URL or default
                            val lastUrl = sharedPreferences.getString(LAST_URL_KEY, DEFAULT_URL) ?: DEFAULT_URL
                            if (view.url != lastUrl) {
                                view.loadUrl(lastUrl)
                            }
                        }
                    }
                    return true // Block the external navigation
                }
            }
            
            override fun onPageFinished(view: WebView, url: String) {
                super.onPageFinished(view, url)

                // Clear loading timeout since page finished
                loadingTimeoutRunnable?.let { handler.removeCallbacks(it) }

                // Save the current URL
                saveCurrentUrl(url)

                // Check if page loaded properly (not blank/black screen)
                view.evaluateJavascript("""
                    (function() {
                        // Check if page has actual content
                        const hasContent = document.body &&
                                         (document.body.children.length > 0 ||
                                          document.body.textContent.trim().length > 0);
                        return hasContent;
                    })();
                """.trimIndent()) { result ->
                    if (result == "false" || result == "null") {
                        // Page seems empty, try to reload or go back
                        android.util.Log.d("ANIME", "Detected empty page, attempting recovery")
                        runOnUiThread {
                            if (view.canGoBack()) {
                                view.goBack()
                            } else {
                                val lastUrl = sharedPreferences.getString(LAST_URL_KEY, DEFAULT_URL) ?: DEFAULT_URL
                                if (url != lastUrl) {
                                    view.loadUrl(lastUrl)
                                }
                            }
                        }
                    }
                }

                // Enhanced popup and ad blocking with navigation bar removal
                view.evaluateJavascript("""
                    (function(){
                        // Hide navigation bar and other UI elements - SPECIFICALLY targeting HiAnime elements
                        const hideElements = `
                            /* Hide HiAnime specific navigation elements */
                            #header, .header, .site-header, .main-header, .top-header,
                            .navbar, .nav-bar, .navigation, .top-bar, .page-header, .app-header,
                            .fixed-top, .sticky-top, .header-wrapper, .header-container,
                            [class*="navbar"], [class*="nav-bar"], [class*="header"], [class*="top-bar"],
                            [id*="header"], [id*="nav"], [class*="nav"], .nav,

                            /* Hide popups and overlays */
                            .popup, .overlay, .modal, .advertisement, .ad-banner, .popup-overlay,
                            .modal-overlay, .ad-container, .toast, .notification, .alert,
                            [class*="popup"], [class*="overlay"], [class*="modal"], [class*="toast"],
                            [id*="popup"], [id*="overlay"], [id*="modal"], [id*="toast"],

                            /* Hide ads */
                            .ad, .ads, .advert, .advertisement, .banner, .sponsor,
                            [class*="ad-"], [class*="ads-"], [id*="ad-"], [id*="ads-"],
                            [class*="advert"], [class*="banner"], [class*="sponsor"],

                            /* Hide common popup containers */
                            .fancybox, .lightbox, .colorbox, .thickbox, .shadowbox,
                            .dialog, .tooltip, .dropdown-menu, .context-menu,

                            /* Hide video overlays and controls that might be ads */
                            .video-overlay, .player-overlay, .controls-overlay,
                            [class*="overlay"], [class*="cover"],

                            /* Hide notification bars */
                            .notification-bar, .cookie-notice, .gdpr-notice, .privacy-notice
                            {
                                display: none !important;
                                visibility: hidden !important;
                                opacity: 0 !important;
                                pointer-events: none !important;
                                position: absolute !important;
                                left: -9999px !important;
                                top: -9999px !important;
                                width: 0 !important;
                                height: 0 !important;
                                z-index: -1 !important;
                            }

                            /* Ensure main content takes full space */
                            body, html {
                                margin: 0 !important;
                                padding: 0 !important;
                                overflow-x: hidden !important;
                            }

                            /* AGGRESSIVE HEADER HIDING - Target any element at the top */
                            *[style*="position: fixed"], *[style*="position:fixed"],
                            *[style*="top: 0"], *[style*="top:0"] {
                                display: none !important;
                            }

                            /* Hide elements that are likely headers based on position */
                            body > *:first-child,
                            body > div:first-child,
                            html > body > *:first-child {
                                display: none !important;
                            }

                            /* Force hide any element containing HiAnime text that's positioned like a header */
                            *:contains("HiAnime"), *:contains("Hi Anime") {
                                display: none !important;
                            }
                        `;

                        let style = document.createElement('style');
                        style.appendChild(document.createTextNode(hideElements));
                        document.head.appendChild(style);

                        // Function to remove elements
                        function removeUnwantedElements() {
                            // Remove HiAnime specific navigation elements - AGGRESSIVE TARGETING
                            const navSelectors = [
                                '#header', '.header', '.site-header', '.main-header', '.page-header',
                                '.app-header', '.top-header', '.header-wrapper', '.header-container',
                                '.navbar', '.nav-bar', '.navigation', '.top-bar', '.nav',
                                '[class*="navbar"]', '[class*="nav-bar"]', '[class*="header"]',
                                '[class*="top-bar"]', '[id*="header"]', '[id*="nav"]', '[class*="nav"]',
                                '.fixed-top', '.sticky-top', 'header', 'nav'
                            ];

                            // Remove popup elements
                            const popupSelectors = [
                                '.popup', '.overlay', '.modal', '.toast', '.notification',
                                '.alert', '.dialog', '.tooltip', '.fancybox', '.lightbox',
                                '[class*="popup"]', '[class*="overlay"]', '[class*="modal"]',
                                '[id*="popup"]', '[id*="overlay"]', '[id*="modal"]'
                            ];

                            // Remove ad elements
                            const adSelectors = [
                                '.ad', '.ads', '.advert', '.advertisement', '.banner',
                                '.sponsor', '[class*="ad-"]', '[class*="ads-"]',
                                '[id*="ad-"]', '[id*="ads-"]', '[class*="advert"]',
                                '[class*="banner"]', '[class*="sponsor"]'
                            ];

                            [...navSelectors, ...popupSelectors, ...adSelectors].forEach(selector => {
                                try {
                                    document.querySelectorAll(selector).forEach(el => {
                                        if (el && el.parentNode) {
                                            el.parentNode.removeChild(el);
                                        }
                                    });
                                } catch (e) {
                                    // Ignore errors for invalid selectors
                                }
                            });

                            // Remove any fixed positioned elements
                            document.querySelectorAll('*').forEach(el => {
                                const style = window.getComputedStyle(el);
                                if (style.position === 'fixed' &&
                                    (el.offsetHeight > 50 || el.offsetWidth > 200)) {
                                    // Likely a popup or navigation bar
                                    if (el.parentNode) {
                                        el.parentNode.removeChild(el);
                                    }
                                }
                            });

                            // SPECIFIC HIANIME HEADER REMOVAL - Check for elements containing "HiAnime" text
                            document.querySelectorAll('*').forEach(el => {
                                const text = el.textContent || el.innerText || '';
                                const rect = el.getBoundingClientRect();

                                // Remove elements that:
                                // 1. Contain "HiAnime" text
                                // 2. Are positioned at the top of the page (y < 100)
                                // 3. Have significant width (likely header)
                                if ((text.toLowerCase().includes('hianime') ||
                                     text.toLowerCase().includes('hi anime')) &&
                                    rect.top < 100 && rect.width > 200) {
                                    console.log('Removing HiAnime header element:', el);
                                    if (el.parentNode) {
                                        el.parentNode.removeChild(el);
                                    }
                                }

                                // Also remove any element that's at the very top and spans most of the width
                                if (rect.top <= 10 && rect.width > window.innerWidth * 0.8 &&
                                    rect.height > 30 && rect.height < 150) {
                                    console.log('Removing top header element:', el);
                                    if (el.parentNode) {
                                        el.parentNode.removeChild(el);
                                    }
                                }
                            });
                        }

                        // Fix white screen by ensuring body has content
                        if (document.body) {
                            document.body.style.backgroundColor = 'black';
                            document.body.style.color = 'white';
                        }
                        if (document.documentElement) {
                            document.documentElement.style.backgroundColor = 'black';
                        }

                        // Run immediately
                        removeUnwantedElements();

                        // Run again after delays to catch dynamically loaded content
                        setTimeout(removeUnwantedElements, 1000);
                        setTimeout(removeUnwantedElements, 3000);
                        setTimeout(removeUnwantedElements, 5000);

                        // Set up mutation observer to catch new popups
                        const observer = new MutationObserver(function(mutations) {
                            mutations.forEach(function(mutation) {
                                if (mutation.addedNodes.length > 0) {
                                    setTimeout(removeUnwantedElements, 100);
                                }
                            });
                        });

                        observer.observe(document.body, {
                            childList: true,
                            subtree: true
                        });

                        // Block common popup events
                        window.addEventListener('beforeunload', function(e) {
                            e.preventDefault();
                            return undefined;
                        });

                        // Override common popup functions
                        window.open = function() { return null; };
                        window.alert = function() { return false; };
                        window.confirm = function() { return false; };
                        window.prompt = function() { return null; };

                    })();
                """.trimIndent(), null)
            }
            
            override fun shouldInterceptRequest(
                view: WebView?, request: WebResourceRequest?
            ): WebResourceResponse? {
                val url = request?.url?.toString() ?: ""

                // Block ad and tracking domains
                val blockedDomains = listOf(
                    "doubleclick.net", "googleadservices.com", "googlesyndication.com",
                    "amazon-adsystem.com", "facebook.com/tr", "google-analytics.com",
                    "googletagmanager.com", "adsystem.amazon.com", "ads.yahoo.com",
                    "bing.com/ads", "outbrain.com", "taboola.com", "criteo.com",
                    "adsystem", "adnxs.com", "adsystem.amazon", "ads.", "/ads/",
                    "popup", "popunder", "interstitial"
                )

                if (blockedDomains.any { url.contains(it, ignoreCase = true) }) {
                    // Return empty response to block the request
                    return WebResourceResponse("text/plain", "utf-8", null)
                }

                return super.shouldInterceptRequest(view, request)
            }

            override fun onReceivedError(
                view: WebView?, request: WebResourceRequest?, error: WebResourceError?
            ) {
                super.onReceivedError(view, request, error)

                // Handle errors gracefully - prevent black screen
                val errorUrl = request?.url?.toString() ?: ""
                android.util.Log.d("ANIME", "WebView error for URL: $errorUrl, Error: ${error?.description}")

                // If main frame failed to load, try to recover
                if (request?.isForMainFrame == true) {
                    runOnUiThread {
                        // Try to go back if possible
                        if (view?.canGoBack() == true) {
                            view.goBack()
                        } else {
                            // Load the default URL as fallback
                            view?.loadUrl(DEFAULT_URL)
                        }
                    }
                }
            }

            override fun onReceivedHttpError(
                view: WebView?, request: WebResourceRequest?, errorResponse: WebResourceResponse?
            ) {
                super.onReceivedHttpError(view, request, errorResponse)

                // Handle HTTP errors (4xx, 5xx) gracefully
                val errorUrl = request?.url?.toString() ?: ""
                android.util.Log.d("ANIME", "HTTP error for URL: $errorUrl, Status: ${errorResponse?.statusCode}")

                // If main frame got HTTP error, try to recover
                if (request?.isForMainFrame == true && errorResponse?.statusCode != 200) {
                    runOnUiThread {
                        if (view?.canGoBack() == true) {
                            view.goBack()
                        } else {
                            view?.loadUrl(DEFAULT_URL)
                        }
                    }
                }
            }
        }
    }
    
    private fun loadLastUrl() {
        val lastUrl = sharedPreferences.getString(LAST_URL_KEY, DEFAULT_URL) ?: DEFAULT_URL
        webView.loadUrl(lastUrl)
    }
    
    private fun saveCurrentUrl(url: String) {
        // Only save URLs from hianime.to domain
        if (url.contains(HIANIME_DOMAIN)) {
            sharedPreferences.edit()
                .putString(LAST_URL_KEY, url)
                .apply()
        }
    }
    
    override fun onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack()
        } else {
            super.onBackPressed()
        }
    }
    
    override fun onPause() {
        super.onPause()
        // Save current URL when app goes to background
        saveCurrentUrl(webView.url ?: DEFAULT_URL)
    }
    
    override fun onResume() {
        super.onResume()
        // Ensure fullscreen mode is maintained when app resumes
        hideSystemUI()
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
            hideSystemUI()
        }
    }

    private fun hideSystemUI() {
        // Hide system bars for immersive fullscreen experience
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
        windowInsetsController.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        windowInsetsController.hide(WindowInsetsCompat.Type.systemBars())

        // Additional fullscreen flags for older Android versions
        @Suppress("DEPRECATION")
        window.decorView.systemUiVisibility = (
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            or View.SYSTEM_UI_FLAG_FULLSCREEN
        )
    }

    override fun onDestroy() {
        super.onDestroy()
        webView.destroy()
    }
}
